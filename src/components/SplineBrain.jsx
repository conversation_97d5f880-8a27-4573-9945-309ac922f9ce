'use client';
import { Suspense, useEffect, useRef, useState } from "react";
import { Application } from '@splinetool/runtime';

const SplineBrain = () => {
  const canvasRef = useRef(null);
  const appRef = useRef(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    let mounted = true;

    const initSpline = async () => {
      if (!canvasRef.current || appRef.current) return;

      try {
        setIsLoading(true);
        setHasError(false);
        console.log('Initializing Spline application...');

        const app = new Application(canvasRef.current);
        appRef.current = app;

        // Use a simple, known working Spline scene for testing
        const splineUrl = 'https://prod.spline.design/6Wq5p8QOSplGIjjV/scene.splinecode';

        console.log('Loading Spline scene from:', splineUrl);

        // Add timeout to prevent hanging
        const loadPromise = app.load(splineUrl);
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Spline loading timeout')), 10000)
        );

        await Promise.race([loadPromise, timeoutPromise]);

        if (mounted) {
          setIsLoading(false);
          console.log('Spline scene loaded successfully');
        }
      } catch (error) {
        console.error('Error loading Spline scene:', error);
        if (mounted) {
          setHasError(true);
          setIsLoading(false);
        }
      }
    };

    // Add a small delay to ensure canvas is properly mounted
    const timer = setTimeout(initSpline, 200);

    // Cleanup function
    return () => {
      mounted = false;
      clearTimeout(timer);
      if (appRef.current) {
        try {
          appRef.current.dispose();
        } catch (error) {
          console.warn('Error disposing Spline app:', error);
        }
        appRef.current = null;
      }
    };
  }, []);

  if (hasError) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <div className="text-center text-gray-400">
          {/* Animated Brain Fallback */}
          <div className="relative w-32 h-32 mx-auto mb-4">
            <div className="absolute inset-0 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 opacity-20 animate-pulse"></div>
            <div className="absolute inset-2 rounded-full bg-gradient-to-r from-purple-400 to-blue-400 opacity-30 animate-ping"></div>
            <div className="absolute inset-4 rounded-full bg-gradient-to-r from-purple-300 to-blue-300 opacity-40 animate-bounce"></div>
            <div className="absolute inset-0 flex items-center justify-center">
              <span className="text-4xl animate-pulse">🧠</span>
            </div>
          </div>
          <p className="text-sm font-medium">3D Brain Model</p>
          <p className="text-xs text-gray-500 mt-1">Interactive visualization</p>
        </div>
      </div>
    );
  }

  return (
    <div className="spline-container">
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center z-10 bg-black/20 backdrop-blur-sm">
          <div className="text-center text-gray-300">
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-purple-500 mx-auto mb-4"></div>
            <p className="text-sm font-medium">Loading 3D Brain...</p>
          </div>
        </div>
      )}
      <canvas
        ref={canvasRef}
        className="spline-canvas"
        style={{
          background: 'transparent',
          outline: 'none'
        }}
      />
    </div>
  );
};

const SplineBrainCanvas = () => {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    // Add a small delay to ensure proper mounting
    const timer = setTimeout(() => setIsMounted(true), 100);
    return () => {
      clearTimeout(timer);
      setIsMounted(false);
    };
  }, []);

  if (!isMounted) {
    return (
      <div className="w-full h-full flex items-center justify-center min-h-[300px]">
        <div className="text-center text-gray-400">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto mb-4"></div>
          <p className="text-sm">Initializing...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full min-h-[300px]">
      <Suspense fallback={
        <div className="w-full h-full flex items-center justify-center">
          <div className="text-center text-gray-400">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto mb-4"></div>
            <p className="text-sm">Loading 3D Scene...</p>
          </div>
        </div>
      }>
        <SplineBrain />
      </Suspense>
    </div>
  );
};

export default SplineBrainCanvas;

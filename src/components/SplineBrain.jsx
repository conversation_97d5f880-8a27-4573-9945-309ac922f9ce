'use client';
import { Suspense, useEffect, useRef } from "react";
import { Application } from '@splinetool/runtime';

const SplineBrain = () => {
  const canvasRef = useRef(null);
  const appRef = useRef(null);

  useEffect(() => {
    if (canvasRef.current && !appRef.current) {
      const app = new Application(canvasRef.current);
      appRef.current = app;
      
      // Load your Spline scene
      app.load('https://prod.spline.design/lMTtT7ImgXrj6CWf9BEMwTeY/scene.splinecode')
        .catch(error => {
          console.error('Error loading Spline scene:', error);
        });
    }

    // Cleanup function
    return () => {
      if (appRef.current) {
        appRef.current.dispose();
        appRef.current = null;
      }
    };
  }, []);

  return (
    <div className="w-full h-full relative">
      <canvas 
        ref={canvasRef} 
        className="w-full h-full"
        style={{ 
          background: 'transparent',
          outline: 'none'
        }}
      />
    </div>
  );
};

const SplineBrainCanvas = () => {
  return (
    <div className="w-full h-full">
      <Suspense fallback={
        <div className="w-full h-full flex items-center justify-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-500"></div>
        </div>
      }>
        <SplineBrain />
      </Suspense>
    </div>
  );
};

export default SplineBrainCanvas;

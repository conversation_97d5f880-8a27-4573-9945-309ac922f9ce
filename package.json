{"name": "portfolio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emailjs/browser": "^4.4.1", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@gltf-transform/cli": "^4.1.3", "@mui/icons-material": "^7.0.1", "@react-three/drei": "^10.0.5", "@react-three/fiber": "^9.1.0", "@splinetool/runtime": "^1.10.37", "@use-gesture/react": "^10.3.1", "aos": "^2.3.4", "firebase": "^11.5.0", "framer-motion": "^12.6.2", "gsap": "^3.12.7", "lucide-react": "^0.485.0", "next": "15.2.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-parallax-tilt": "^1.7.288", "react-swipeable": "^7.0.2", "react-use-gesture": "^9.1.3", "styled-components": "^6.1.17", "sweetalert2": "^11.17.2", "three": "^0.175.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "eslint": "^9", "eslint-config-next": "15.2.4", "tailwindcss": "^4"}}